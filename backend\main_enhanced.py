from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List
import os
from dotenv import load_dotenv
import json
import logging
import asyncio
import time
from google import genai

from services.prompt_templates import PromptTemplates
from services.file_processor import FileProcessor

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="SynTour AI API - Enhanced",
    description="Advanced FastAPI backend for SynTour travel planning with multimodal AI support",
    version="2.0.0"
)

# CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3007",
        "http://localhost:3008",
        "http://localhost:3009",
        "http://localhost:3000"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Enhanced Request/Response models
class ChatRequest(BaseModel):
    message: str
    context: Optional[str] = None
    user_preferences: Optional[dict] = None

class MultimodalRequest(BaseModel):
    message: str
    context: Optional[str] = None
    file_descriptions: Optional[dict] = None

class ChatResponse(BaseModel):
    success: bool
    response: Optional[str] = None
    error: Optional[str] = None
    model: str = "gemini-1.5-flash"
    processing_info: Optional[dict] = None
    is_truncated: Optional[bool] = False
    continuation_id: Optional[str] = None

class FileUploadResponse(BaseModel):
    success: bool
    file_info: Optional[dict] = None
    processed_content: Optional[str] = None
    error: Optional[str] = None

# Helper function to detect truncated responses
def is_response_truncated(response_text: str) -> bool:
    """Detect if a response appears to be truncated"""
    if not response_text:
        return False

    # Check for common truncation indicators
    truncation_indicators = [
        # Incomplete sentences
        response_text.endswith(('...', '..', '..')),
        # Ends mid-sentence without proper punctuation
        not response_text.strip().endswith(('.', '!', '?', '"', "'", ')', ']', '}')),
        # Ends with incomplete markdown
        response_text.count('```') % 2 != 0,
        response_text.count('**') % 2 != 0,
        response_text.count('*') % 2 != 0,
        # Ends with incomplete list item
        response_text.strip().endswith(('*', '-', '1.', '2.', '3.', '4.', '5.')),
        # Very long response that might hit token limit
        len(response_text) > 3500,
    ]

    return any(truncation_indicators)

# Store for continuation contexts
continuation_store = {}

# Initialize Google Gen AI SDK
def initialize_genai():
    """Initialize Google Gen AI SDK"""
    try:
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        location = os.getenv("GOOGLE_CLOUD_LOCATION")
        credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")

        if not all([project_id, location]):
            raise ValueError("Missing GOOGLE_CLOUD_PROJECT or GOOGLE_CLOUD_LOCATION")

        if credentials_path and not os.path.exists(credentials_path):
            raise ValueError(f"Service account key file not found: {credentials_path}")

        # Google Gen AI SDK handles initialization automatically
        logger.info(f"✅ Initialized Google Gen AI for project: {project_id}")
        logger.info(f"✅ Using credentials: {credentials_path}")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to initialize Google Gen AI: {str(e)}")
        return False

# Initialize on startup
@app.on_event("startup")
async def startup_event():
    if initialize_genai():
        logger.info("🚀 FastAPI server started successfully with Google Gen AI")
    else:
        logger.warning("⚠️ FastAPI server started but Google Gen AI initialization failed")

# Health check endpoints
@app.get("/")
async def root():
    return {
        "message": "SynTour AI API Enhanced - Running",
        "status": "healthy",
        "version": "2.0.0",
        "features": ["multimodal", "travel_planning", "file_upload", "speech_recognition"]
    }

@app.get("/health")
async def health_check():
    genai_status = "connected" if initialize_genai() else "disconnected"
    return {
        "status": "healthy",
        "service": "fastapi-backend-enhanced",
        "genai": genai_status
    }

# Test endpoint for Vertex AI Gemini
@app.post("/test-vertex")
async def test_vertex_endpoint(request: ChatRequest):
    try:
        logger.info(f"Test Vertex AI request: {request.message[:50]}...")

        # --- CORRECTED CODE using Google Gen AI SDK ---

        # 1. Initialize the client
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        location = os.getenv("GOOGLE_CLOUD_LOCATION")

        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )

        # 2. Define your fine-tuned model's full resource name
        model_name = os.getenv("VERTEX_AI_ENDPOINT")

        # 3. Create prompt
        prompt = f"You are a helpful travel assistant. User: {request.message}\n\nAssistant:"

        # 4. Create content and config
        contents = [
            genai.types.Content(
                role="user",
                parts=[genai.types.Part(text=prompt)]
            )
        ]

        config = genai.types.GenerateContentConfig(
            temperature=0.7,
            top_p=0.9,
            max_output_tokens=2048,
        )

        # 5. Generate content using the correct API
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=config,
        )

        # 6. Extract the response text
        ai_response = response.text if response.text else "No response generated"

        # --- END CORRECTED CODE ---

        logger.info(f"Test Vertex AI response generated successfully")

        return ChatResponse(
            success=True,
            response=ai_response,
            model="fine-tuned-gemini-test",
            processing_info={"prompt_type": "test", "endpoint": "vertex"}
        )

    except Exception as e:
        logger.error(f"Test Vertex AI error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Test failed: {str(e)}",
            model="fine-tuned-gemini-test"
        )

# Simple chat endpoint (following malaysia-ai-backend pattern)
@app.post("/chat", response_model=ChatResponse)
async def simple_chat(request: ChatRequest):
    try:
        logger.info(f"Simple chat request: {request.message[:50]}...")

        # --- CORRECTED CODE using Google Gen AI SDK ---

        # 1. Initialize the client
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        location = os.getenv("GOOGLE_CLOUD_LOCATION")

        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )

        # 2. Define your fine-tuned model's full resource name
        model_name = os.getenv("VERTEX_AI_ENDPOINT")

        # 3. Simple prompt without complex templates
        prompt = f"You are a helpful travel assistant. User: {request.message}\n\nAssistant:"

        # 4. Create content and config
        contents = [
            genai.types.Content(
                role="user",
                parts=[genai.types.Part(text=prompt)]
            )
        ]

        config = genai.types.GenerateContentConfig(
            temperature=0.7,
            top_p=0.9,
            max_output_tokens=4096,
        )

        # 5. Generate content using the correct API
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=config,
        )

        # 6. Extract the response text
        ai_response = response.text if response.text else "No response generated"

        # --- END CORRECTED CODE ---

        logger.info(f"Simple chat response generated successfully")

        return ChatResponse(
            success=True,
            response=ai_response,
            model="fine-tuned-gemini",
            processing_info={"prompt_type": "simple", "endpoint": "custom"}
        )

    except Exception as e:
        logger.error(f"Simple chat error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Failed to generate response: {str(e)}",
            model="fine-tuned-gemini"
        )

# File upload endpoint
@app.post("/api/upload", response_model=FileUploadResponse)
async def upload_file(file: UploadFile = File(...)):
    try:
        # Read file content
        file_content = await file.read()
        
        # Get file info
        file_info = FileProcessor.get_file_info(file.filename)
        
        if not file_info["supported"]:
            return FileUploadResponse(
                success=False,
                error=f"Unsupported file type: {file.filename}"
            )
        
        # Process file
        processed = await FileProcessor.process_file(file_content, file.filename)
        
        if processed.get("error"):
            return FileUploadResponse(
                success=False,
                error=processed["error"]
            )
        
        return FileUploadResponse(
            success=True,
            file_info=file_info,
            processed_content=processed.get("description", "File processed successfully")
        )
        
    except Exception as e:
        logger.error(f"File upload error: {str(e)}")
        return FileUploadResponse(
            success=False,
            error=f"File upload failed: {str(e)}"
        )

# Enhanced AI Chat endpoint with prompt engineering
@app.post("/api/ai/chat", response_model=ChatResponse)
async def chat_with_ai(request: ChatRequest):
    try:
        print(f"🔥 DEBUG: Received chat request: {request.message[:50]}...")
        logger.info(f"Received chat request: {request.message[:50]}...")

        # --- CORRECTED CODE using Google Gen AI SDK ---

        # 1. Initialize the client
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        location = os.getenv("GOOGLE_CLOUD_LOCATION")

        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )

        # 2. Define your fine-tuned model's full resource name
        model_name = os.getenv("VERTEX_AI_ENDPOINT")

        # 3. Use professional prompt templates
        if request.context and "travel" in request.context.lower():
            prompt = PromptTemplates.travel_planning_prompt(
                user_message=request.message,
                duration=request.user_preferences.get("duration") if request.user_preferences else None,
                budget=request.user_preferences.get("budget") if request.user_preferences else None
            )
        else:
            system_prompt = PromptTemplates.get_system_prompt()
            prompt = f"{system_prompt}\n\nUser: {request.message}\n\nAssistant:"

        logger.info(f"Generating content with model: {model_name}")
        logger.info(f"Prompt length: {len(prompt)}")

        # 4. Create content and config
        contents = [
            genai.types.Content(
                role="user",
                parts=[genai.types.Part(text=prompt)]
            )
        ]

        config = genai.types.GenerateContentConfig(
            temperature=0.7,
            top_p=0.9,
            max_output_tokens=4096,
        )

        # 5. Generate content using the correct API
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=config,
        )

        # 6. Extract the response text
        ai_response = response.text if response.text else "No response generated"

        logger.info(f"Generated response length: {len(ai_response) if ai_response else 0}")

        # Check if response is truncated
        truncated = is_response_truncated(ai_response)
        continuation_id = None

        if truncated:
            # Store context for potential continuation
            continuation_id = f"cont_{hash(request.message + str(time.time()))}"
            continuation_store[continuation_id] = {
                "original_message": request.message,
                "context": request.context,
                "user_preferences": request.user_preferences,
                "partial_response": ai_response,
                "timestamp": time.time()
            }
            logger.info(f"Response appears truncated, stored continuation context: {continuation_id}")

        # --- END CORRECTED CODE ---

        logger.info(f"Generated response: {ai_response[:100]}...")

        return ChatResponse(
            success=True,
            response=ai_response,
            model="fine-tuned-gemini",
            processing_info={"prompt_type": "professional_template", "endpoint": "custom"},
            is_truncated=truncated,
            continuation_id=continuation_id
        )

    except Exception as e:
        logger.error(f"AI chat error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Failed to generate AI response: {str(e)}",
            model="fine-tuned-gemini"
        )

# Multimodal AI endpoint
@app.post("/api/ai/multimodal", response_model=ChatResponse)
async def multimodal_ai_chat(
    message: str = Form(...),
    context: Optional[str] = Form(None),
    files: List[UploadFile] = File(None)
):
    try:
        # --- CORRECTED CODE using Google Gen AI SDK ---

        # 1. Initialize the client
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        location = os.getenv("GOOGLE_CLOUD_LOCATION")

        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )

        # 2. Define your fine-tuned model's full resource name
        model_name = os.getenv("VERTEX_AI_ENDPOINT")

        # Process uploaded files
        media_descriptions = {}
        media_types = []

        if files:
            for file in files:
                file_content = await file.read()
                processed = await FileProcessor.process_file(file_content, file.filename)

                if processed.get("processed"):
                    file_info = FileProcessor.get_file_info(file.filename)
                    media_types.append(file_info["type"])
                    media_descriptions[file_info["type"]] = processed.get("description", "")

        # Generate multimodal prompt
        if media_types:
            prompt = PromptTemplates.multimodal_prompt(
                user_message=message,
                media_types=media_types,
                media_descriptions=media_descriptions
            )
        else:
            # Fallback to regular travel prompt
            prompt = PromptTemplates.travel_planning_prompt(message)

        # 3. Create content and config
        contents = [
            genai.types.Content(
                role="user",
                parts=[genai.types.Part(text=prompt)]
            )
        ]

        config = genai.types.GenerateContentConfig(
            temperature=0.7,
            top_p=0.9,
            max_output_tokens=4096,
        )

        # 4. Generate content using the correct API
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=config,
        )

        # 5. Extract the response text
        ai_response = response.text if response.text else "No response generated"

        # --- END CORRECTED CODE ---

        return ChatResponse(
            success=True,
            response=ai_response,
            model="fine-tuned-gemini",
            processing_info={
                "prompt_type": "multimodal" if media_types else "text_only",
                "media_types": media_types,
                "files_processed": len(files) if files else 0
            }
        )

    except Exception as e:
        logger.error(f"Multimodal AI error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Failed to process multimodal request: {str(e)}"
        )

# Specialized travel planning endpoint
@app.post("/api/travel/plan", response_model=ChatResponse)
async def create_travel_plan(request: ChatRequest):
    try:
        # --- CORRECTED CODE using Google Gen AI SDK ---

        # 1. Initialize the client
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        location = os.getenv("GOOGLE_CLOUD_LOCATION")

        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )

        # 2. Define your fine-tuned model's full resource name
        model_name = os.getenv("VERTEX_AI_ENDPOINT")

        # Extract travel parameters from user preferences
        duration = None
        budget = None
        interests = None
        travelers = None

        if request.user_preferences:
            duration = request.user_preferences.get("duration")
            budget = request.user_preferences.get("budget")
            interests = request.user_preferences.get("interests")
            travelers = request.user_preferences.get("travelers")

        # Use specialized travel planning prompt
        prompt = PromptTemplates.travel_planning_prompt(
            user_message=request.message,
            duration=duration,
            budget=budget,
            interests=interests,
            travelers=travelers
        )

        # 3. Create content and config
        contents = [
            genai.types.Content(
                role="user",
                parts=[genai.types.Part(text=prompt)]
            )
        ]

        config = genai.types.GenerateContentConfig(
            temperature=0.7,
            top_p=0.9,
            max_output_tokens=4096,
        )

        # 4. Generate content using the correct API
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=config,
        )

        # 5. Extract the response text
        ai_response = response.text if response.text else "No response generated"

        # --- END CORRECTED CODE ---

        return ChatResponse(
            success=True,
            response=ai_response,
            model="fine-tuned-gemini",
            processing_info={"prompt_type": "specialized_travel_planning"}
        )

    except Exception as e:
        logger.error(f"Travel planning error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Failed to create travel plan: {str(e)}"
        )

# Continuation endpoint for truncated responses
@app.post("/api/ai/continue/{continuation_id}", response_model=ChatResponse)
async def continue_response(continuation_id: str):
    try:
        # Check if continuation context exists
        if continuation_id not in continuation_store:
            return ChatResponse(
                success=False,
                error="Continuation context not found or expired"
            )

        context = continuation_store[continuation_id]

        # Check if context is not too old (1 hour limit)
        if time.time() - context["timestamp"] > 3600:
            del continuation_store[continuation_id]
            return ChatResponse(
                success=False,
                error="Continuation context has expired"
            )

        # Initialize the client
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        location = os.getenv("GOOGLE_CLOUD_LOCATION")

        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )

        model_name = os.getenv("VERTEX_AI_ENDPOINT")

        # Create continuation prompt
        continuation_prompt = f"""Please continue the following response where it was cut off:

Original question: {context['original_message']}

Previous response (incomplete):
{context['partial_response']}

Please continue from where the response was cut off, maintaining the same tone and style:"""

        contents = [
            genai.types.Content(
                role="user",
                parts=[genai.types.Part(text=continuation_prompt)]
            )
        ]

        config = genai.types.GenerateContentConfig(
            temperature=0.7,
            top_p=0.9,
            max_output_tokens=4096,
        )

        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=config,
        )

        ai_response = response.text if response.text else "No continuation generated"

        # Clean up the continuation context
        del continuation_store[continuation_id]

        return ChatResponse(
            success=True,
            response=ai_response,
            model="fine-tuned-gemini",
            processing_info={"prompt_type": "continuation", "endpoint": "custom"}
        )

    except Exception as e:
        logger.error(f"Continuation error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Failed to continue response: {str(e)}"
        )

# Voice chat endpoint
@app.post("/api/ai/voice", response_model=ChatResponse)
async def voice_chat(audio_file: UploadFile = File(...)):
    try:
        # Process audio file for speech recognition
        file_content = await audio_file.read()
        processed = await FileProcessor.process_file(file_content, audio_file.filename)

        if not processed.get("processed"):
            return ChatResponse(
                success=False,
                error=processed.get("error", "Failed to process audio file")
            )

        # Extract transcribed text
        transcribed_text = processed.get("content", "")

        if not transcribed_text or "[Speech could not be understood]" in transcribed_text:
            return ChatResponse(
                success=False,
                error="Could not understand speech in audio file"
            )

        # --- CORRECTED CODE using Google Gen AI SDK ---

        # 1. Initialize the client
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        location = os.getenv("GOOGLE_CLOUD_LOCATION")

        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )

        # 2. Define your fine-tuned model's full resource name
        model_name = os.getenv("VERTEX_AI_ENDPOINT")

        # 3. Create voice-optimized prompt
        prompt = PromptTemplates.voice_response_prompt(transcribed_text, is_voice_request=True)

        # 4. Create content and config
        contents = [
            genai.types.Content(
                role="user",
                parts=[genai.types.Part(text=prompt)]
            )
        ]

        config = genai.types.GenerateContentConfig(
            temperature=0.7,
            top_p=0.9,
            max_output_tokens=2048,
        )

        # 5. Generate content using the correct API
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=config,
        )

        # 6. Extract the response text
        ai_response = response.text if response.text else "No response generated"

        # --- END CORRECTED CODE ---

        return ChatResponse(
            success=True,
            response=ai_response,
            model="fine-tuned-gemini",
            processing_info={
                "prompt_type": "voice_optimized",
                "transcribed_text": transcribed_text,
                "audio_duration": processed.get("metadata", {}).get("duration_seconds", 0)
            }
        )

    except Exception as e:
        logger.error(f"Voice chat error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Failed to process voice request: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main_enhanced:app", host="0.0.0.0", port=8002, reload=True)
