"use client";
import { useRef, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import clsx from "clsx";
import { AIService } from "@/services/aiService";

export interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  files?: { name: string; type: string }[];
  timestamp: Date;
  error?: string;
  is_truncated?: boolean;
  continuation_id?: string;
}

interface ChatInterfaceProps {
  messages: Message[];
  isLoading: boolean;
  onUpdateMessage?: (messageId: string, updates: Partial<Message>) => void;
}

export default function ChatInterface({ messages, isLoading, onUpdateMessage }: ChatInterfaceProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [continuingMessages, setContinuingMessages] = useState<Set<string>>(new Set());

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isLoading]);

  const handleContinueResponse = async (message: Message) => {
    if (!message.continuation_id || !onUpdateMessage) return;

    setContinuingMessages(prev => new Set(prev).add(message.id));

    try {
      const response = await AIService.continueResponse(message.continuation_id);

      if (response.success && response.response) {
        // Append the continuation to the existing message
        const updatedContent = message.content + response.response;
        onUpdateMessage(message.id, {
          content: updatedContent,
          is_truncated: false,
          continuation_id: undefined
        });
      }
    } catch (error) {
      console.error('Failed to continue response:', error);
    } finally {
      setContinuingMessages(prev => {
        const newSet = new Set(prev);
        newSet.delete(message.id);
        return newSet;
      });
    }
  };

  if (messages.length === 0 && !isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-8">
        <div className="w-16 h-16 rounded-full overflow-hidden flex items-center justify-center mb-4">
          <img 
            src="/branding/syntour.png" 
            alt="SynTour AI" 
            className="w-full h-full object-cover"
          />
        </div>
        <h3 className="text-xl font-semibold text-gray-700 mb-2">
          Welcome to SynTour AI
        </h3>
        <p className="text-gray-500 max-w-md text-sm leading-relaxed">
          Ask me anything about travel planning! I can help with destinations, itineraries,
          recommendations, and more. You can also upload images or files for better assistance.
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-y-auto p-6 space-y-6">
        {messages.map((message, index) => (
          <div
            key={message.id}
            className={clsx(
              "flex items-start gap-4 animate-in slide-in-from-bottom-2 duration-300",
              message.type === 'user' ? 'flex-row-reverse' : 'flex-row'
            )}
            style={{ animationDelay: `${index * 50}ms` }}
          >
            {/* Avatar */}
            <div className={clsx(
              "flex-shrink-0 w-10 h-10 rounded-full overflow-hidden flex items-center justify-center shadow-lg",
              message.type === 'user'
                ? "bg-gradient-to-br from-blue-500 to-purple-600"
                : ""
            )}>
              {message.type === 'user' ? (
                <User className="w-5 h-5 text-white" />
              ) : (
                <img 
                  src="/branding/syntour.png" 
                  alt="SynTour AI" 
                  className="w-full h-full object-cover"
                />
              )}
            </div>

            {/* Message Content */}
            <div className={clsx(
              "max-w-[70%] rounded-2xl p-3 shadow-sm",
              message.type === 'user'
                ? "bg-gradient-to-br from-blue-500 to-purple-600 text-white"
                : message.error 
                  ? "bg-red-50 border border-red-200"
                  : "bg-transparent border border-gray-200/30"
            )}>
              {/* Files if any */}
              {message.files && message.files.length > 0 && (
                <div className="mb-2 flex flex-wrap gap-1">
                  {message.files.map((file, index) => (
                    <span
                      key={index}
                      className={clsx(
                        "text-xs px-2 py-1 rounded-full",
                        message.type === 'user'
                          ? "bg-white/20 text-white"
                          : "bg-gray-100 text-gray-600"
                      )}
                    >
                      📎 {file.name}
                    </span>
                  ))}
                </div>
              )}

              {/* Message text */}
              <div className={clsx(
                "text-sm leading-relaxed",
                message.error
                  ? "text-red-700"
                  : message.type === 'assistant'
                    ? "text-gray-800"
                    : "text-white"
              )}>
                {message.error ? (
                  <>
                    <div className="font-medium mb-1">❌ Error:</div>
                    {message.error}
                  </>
                ) : (
                  <div className="whitespace-pre-wrap">{message.content}</div>
                )}
              </div>

              {/* Truncation Notice & Continue Button */}
              {message.is_truncated && message.continuation_id && message.type === 'assistant' && (
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <button
                    onClick={() => handleContinueResponse(message)}
                    disabled={continuingMessages.has(message.id)}
                    className="inline-flex items-center gap-2 px-3 py-2 text-xs bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-lg transition-colors duration-200 disabled:opacity-50"
                  >
                    {continuingMessages.has(message.id) ? (
                      <>
                        <Loader2 className="w-3 h-3 animate-spin" />
                        Continuing...
                      </>
                    ) : (
                      <>
                        <span>📖</span>
                        Continue Response
                      </>
                    )}
                  </button>
                </div>
              )}

              {/* Timestamp */}
              <div className={clsx(
                "text-xs mt-2 opacity-75",
                message.type === 'user' ? "text-white" : "text-gray-500"
              )}>
                {message.timestamp.toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </div>
            </div>
          </div>
        ))}

        {/* Loading indicator */}
        {isLoading && (
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 w-8 h-8 rounded-full overflow-hidden flex items-center justify-center shadow-lg">
              <img 
                src="/branding/syntour.png" 
                alt="SynTour AI" 
                className="w-full h-full object-cover"
              />
            </div>
            <div className="bg-white border border-gray-200 rounded-2xl p-3 shadow-sm">
              <div className="flex items-center gap-2 text-gray-600">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span className="text-sm">SynTour is thinking...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>
    </div>
  );
}